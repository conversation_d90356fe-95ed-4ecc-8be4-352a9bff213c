{"start": "2025-09-06T02:58:04.765Z", "end": "2025-09-06T02:58:04.766Z", "capabilities": {"platformName": "MobileRequired", "appium:deviceName": "Mobile testing only - setup action required"}, "framework": "mocha", "mochaOpts": {"timeout": 120000, "ui": "bdd"}, "suites": [], "specs": ["file:///Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/tests/master.spec.ts"], "state": {"passed": 0, "failed": 0, "skipped": 0}}