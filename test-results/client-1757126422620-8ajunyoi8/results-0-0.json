{"start": "2025-09-06T02:40:38.823Z", "end": "2025-09-06T02:40:53.970Z", "capabilities": {"platformName": "Android", "df:recordVideo": true, "df:videoQuality": "high", "df:videoFPS": 15, "df:recordVideoOnFailureOnly": false, "df:videoName": "test_1757126425135", "df:videoTimeLimit": null, "df:options": {"saveDeviceLogs": true, "build": "test_1757126425136", "accesskey": "admin_AzZvGSzPbIrrEx", "token": "9de28a88-21b5-4887-8282-848106f3afe5", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757126425135", "videoTimeLimit": null, "options": {"saveDeviceLogs": true, "build": "test_1757126425136", "accesskey": "admin_AzZvGSzPbIrrEx", "token": "9de28a88-21b5-4887-8282-848106f3afe5", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757126425135", "videoTimeLimit": null}, "enableTestStatusReporting": true, "testResultsEndpoint": "/test-results", "logTestResults": true, "markTestStatus": true, "autoMarkTestStatus": true, "testStatusFromLogs": true, "testStatusFromExitCode": true, "forceTestStatusUpdate": true}, "df:enableTestStatusReporting": true, "df:testResultsEndpoint": "/test-results", "df:logTestResults": true, "df:markTestStatus": true, "df:autoMarkTestStatus": true, "df:testStatusFromLogs": true, "df:testStatusFromExitCode": true, "df:forceTestStatusUpdate": true, "platformVersion": "16", "deviceName": "emulator-5554", "automationName": "UiAutomator2", "newCommandTimeout": 300, "sessionOverride": true, "autoGrantPermissions": true, "noReset": false, "fullReset": false, "appWaitActivity": "*", "appWaitDuration": 30000, "androidInstallTimeout": 120000, "uiautomator2ServerInstallTimeout": 60000, "uiautomator2ServerLaunchTimeout": 60000, "skipServerInstallation": false, "skipDeviceInitialization": false, "disableWindowAnimation": false, "skipUnlock": true, "androidDeviceReadyTimeout": 60, "appWaitForLaunch": true, "app": "/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/1756047271343-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk", "udid": "emulator-5554", "systemPort": 56367, "chromeDriverPort": 56368, "adbPort": 5037, "mjpegServerPort": 56369, "platform": "LINUX", "webStorageEnabled": false, "takesScreenshot": true, "javascriptEnabled": true, "databaseEnabled": false, "networkConnectionEnabled": true, "locationContextEnabled": false, "warnings": {}, "desired": {"platformName": "Android", "df:recordVideo": true, "df:videoQuality": "high", "df:videoFPS": 15, "df:recordVideoOnFailureOnly": false, "df:videoName": "test_1757126425135", "df:videoTimeLimit": null, "df:options": {"saveDeviceLogs": true, "build": "test_1757126425136", "accesskey": "admin_AzZvGSzPbIrrEx", "token": "9de28a88-21b5-4887-8282-848106f3afe5", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757126425135", "videoTimeLimit": null, "options": {"saveDeviceLogs": true, "build": "test_1757126425136", "accesskey": "admin_AzZvGSzPbIrrEx", "token": "9de28a88-21b5-4887-8282-848106f3afe5", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757126425135", "videoTimeLimit": null}, "enableTestStatusReporting": true, "testResultsEndpoint": "/test-results", "logTestResults": true, "markTestStatus": true, "autoMarkTestStatus": true, "testStatusFromLogs": true, "testStatusFromExitCode": true, "forceTestStatusUpdate": true}, "df:enableTestStatusReporting": true, "df:testResultsEndpoint": "/test-results", "df:logTestResults": true, "df:markTestStatus": true, "df:autoMarkTestStatus": true, "df:testStatusFromLogs": true, "df:testStatusFromExitCode": true, "df:forceTestStatusUpdate": true, "platformVersion": "16", "deviceName": "Medium_Phone_API_36.0", "automationName": "UiAutomator2", "newCommandTimeout": 300, "sessionOverride": true, "autoGrantPermissions": true, "noReset": false, "fullReset": false, "appWaitActivity": "*", "appWaitDuration": 30000, "androidInstallTimeout": 120000, "uiautomator2ServerInstallTimeout": 60000, "uiautomator2ServerLaunchTimeout": 60000, "skipServerInstallation": false, "skipDeviceInitialization": false, "disableWindowAnimation": false, "skipUnlock": true, "androidDeviceReadyTimeout": 60, "appWaitForLaunch": true, "app": "/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/1756047271343-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk", "udid": "emulator-5554", "systemPort": 56367, "chromeDriverPort": 56368, "adbPort": 5037, "mjpegServerPort": 56369}, "deviceUDID": "emulator-5554", "appPackage": "com.swaglabsmobileapp", "appActivity": "com.swaglabsmobileapp.SplashActivity", "pixelRatio": "2.625", "statBarHeight": 63, "viewportRect": {"left": 0, "top": 63, "width": 1080, "height": 2337}, "deviceApiLevel": 36, "deviceManufacturer": "Google", "deviceModel": "sdk_gphone64_arm64", "deviceScreenSize": "1080x2400", "deviceScreenDensity": 420, "sessionId": "8e606209-eb82-4e78-a867-1d3a08447e5a"}, "framework": "mocha", "mochaOpts": {"timeout": 120000, "ui": "bdd"}, "suites": [{"name": "Test-client-1757126422620-8ajunyoi8", "duration": 13673, "start": "2025-09-06T02:40:38.824Z", "end": "2025-09-06T02:40:52.497Z", "sessionId": "8e606209-eb82-4e78-a867-1d3a08447e5a", "tests": [{"name": "should execute mobile test steps", "start": "2025-09-06T02:40:38.825Z", "end": "2025-09-06T02:40:52.490Z", "duration": 13665, "state": "passed"}], "hooks": []}], "specs": ["file:///Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/tests/master.spec.ts"], "state": {"passed": 1, "failed": 0, "skipped": 0}}